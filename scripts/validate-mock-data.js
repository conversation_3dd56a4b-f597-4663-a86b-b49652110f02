#!/usr/bin/env node

/**
 * Mock Data Validation Script
 * 
 * This script validates that our mock API responses match the exact structure
 * expected by the Interactive Brokers Client Portal Web API.
 */

// Validation script for IBKR API compatibility

// IBKR API Response Schemas
const IBKR_SCHEMAS = {
  portfolioSummary: {
    required: ['totalCashValue', 'netLiquidationValue', 'unrealizedPnl', 'realizedPnl'],
    optional: ['performanceMetrics', 'period', 'lastUpdated'],
    structure: {
      totalCashValue: { type: 'object', fields: ['amount', 'currency'] },
      netLiquidationValue: { type: 'object', fields: ['amount', 'currency'] },
      unrealizedPnl: { type: 'object', fields: ['amount', 'currency'] },
      realizedPnl: { type: 'object', fields: ['amount', 'currency'] },
      performanceMetrics: {
        type: 'object',
        fields: [
          'totalReturnPct', 'dayReturnPct', 'weekReturnPct', 'monthReturnPct',
          'quarterReturnPct', 'yearReturnPct', 'ytdReturnPct', 'maxDrawdownPct',
          'sharpeRatio', 'winRatePct', 'profitFactor', 'totalTrades'
        ]
      }
    }
  },
  
  chartData: {
    required: ['symbol', 'text', 'data'],
    optional: ['period', 'barSize'],
    structure: {
      symbol: { type: 'string' },
      text: { type: 'string' },
      data: {
        type: 'array',
        itemSchema: {
          required: ['t', 'o', 'h', 'l', 'c', 'v'],
          types: {
            t: 'number', // timestamp
            o: 'number', // open
            h: 'number', // high
            l: 'number', // low
            c: 'number', // close
            v: 'number'  // volume
          }
        }
      }
    }
  },
  
  trades: {
    type: 'array',
    itemSchema: {
      required: ['id', 'symbol', 'side', 'quantity', 'returnPct', 'date'],
      types: {
        id: 'string',
        symbol: 'string',
        side: 'string', // 'BUY' | 'SELL'
        quantity: 'number',
        returnPct: 'number',
        date: 'string' // ISO date string
      }
    }
  }
};

// Validation functions
function validateType(value, expectedType) {
  if (expectedType === 'array') {
    return Array.isArray(value);
  }
  return typeof value === expectedType;
}

function validateObject(obj, schema, path = '') {
  const errors = [];
  
  // Check required fields
  if (schema.required) {
    for (const field of schema.required) {
      if (!(field in obj)) {
        errors.push(`Missing required field: ${path}.${field}`);
      }
    }
  }
  
  // Check field types and structures
  if (schema.structure) {
    for (const [field, fieldSchema] of Object.entries(schema.structure)) {
      if (field in obj) {
        const fieldPath = path ? `${path}.${field}` : field;
        
        if (fieldSchema.type === 'object' && fieldSchema.fields) {
          // Validate nested object
          for (const nestedField of fieldSchema.fields) {
            if (!(nestedField in obj[field])) {
              errors.push(`Missing nested field: ${fieldPath}.${nestedField}`);
            }
          }
        } else if (fieldSchema.type === 'array' && fieldSchema.itemSchema) {
          // Validate array items
          if (!Array.isArray(obj[field])) {
            errors.push(`Field ${fieldPath} should be an array`);
          } else {
            obj[field].forEach((item, index) => {
              const itemErrors = validateObject(item, fieldSchema.itemSchema, `${fieldPath}[${index}]`);
              errors.push(...itemErrors);
            });
          }
        } else if (!validateType(obj[field], fieldSchema.type)) {
          errors.push(`Field ${fieldPath} has wrong type. Expected: ${fieldSchema.type}, Got: ${typeof obj[field]}`);
        }
      }
    }
  }
  
  // Check types for simple schemas
  if (schema.types) {
    for (const [field, expectedType] of Object.entries(schema.types)) {
      if (field in obj && !validateType(obj[field], expectedType)) {
        errors.push(`Field ${path}.${field} has wrong type. Expected: ${expectedType}, Got: ${typeof obj[field]}`);
      }
    }
  }
  
  return errors;
}

async function testEndpoint(url, schema, name) {
  console.log(`\n🧪 Testing ${name}...`);
  
  try {
    const response = await fetch(url);
    
    if (!response.ok) {
      console.log(`❌ HTTP Error: ${response.status} ${response.statusText}`);
      return false;
    }
    
    const data = await response.json();
    console.log(`✅ Response received (${JSON.stringify(data).length} bytes)`);
    
    // Validate structure
    const errors = validateObject(data, schema);
    
    if (errors.length === 0) {
      console.log(`✅ ${name} structure is valid`);
      return true;
    } else {
      console.log(`❌ ${name} validation errors:`);
      errors.forEach(error => console.log(`   - ${error}`));
      return false;
    }
    
  } catch (error) {
    console.log(`❌ Request failed: ${error.message}`);
    return false;
  }
}

async function validateMockAPIs() {
  console.log('🔍 Validating Mock API Data Against IBKR Schema\n');
  console.log('=' .repeat(60));
  
  const baseUrl = 'http://localhost:3001';
  const tests = [
    {
      name: 'Portfolio Performance',
      url: `${baseUrl}/api/ibkr/performance?period=1m`,
      schema: IBKR_SCHEMAS.portfolioSummary
    },
    {
      name: 'Chart Data',
      url: `${baseUrl}/api/ibkr/chart-data?period=1w&barSize=1d`,
      schema: IBKR_SCHEMAS.chartData
    }
    // Trade History endpoint removed for privacy protection
  ];
  
  let allPassed = true;
  
  for (const test of tests) {
    const passed = await testEndpoint(test.url, test.schema, test.name);
    allPassed = allPassed && passed;
  }
  
  console.log('\n' + '=' .repeat(60));
  
  if (allPassed) {
    console.log('🎉 All mock APIs are IBKR-compatible!');
    console.log('✅ Ready for production transition to live IBKR data');
  } else {
    console.log('❌ Some APIs need fixes before IBKR integration');
    process.exit(1);
  }
}

// Additional validation for specific IBKR requirements
function validateIBKRSpecificRequirements() {
  console.log('\n🔧 Checking IBKR-specific requirements...');
  
  const requirements = [
    {
      name: 'Timestamp format (Unix milliseconds)',
      check: () => {
        const now = Date.now();
        return now > 1000000000000; // Should be 13 digits for milliseconds
      }
    },
    {
      name: 'Currency object structure',
      check: () => {
        const currencyObj = { amount: 1000, currency: 'USD' };
        return 'amount' in currencyObj && 'currency' in currencyObj;
      }
    },
    {
      name: 'OHLC field naming (t,o,h,l,c,v)',
      check: () => {
        const ohlc = { t: 123, o: 1, h: 2, l: 0.5, c: 1.5, v: 0 };
        return ['t', 'o', 'h', 'l', 'c', 'v'].every(field => field in ohlc);
      }
    }
  ];
  
  requirements.forEach(req => {
    const passed = req.check();
    console.log(`${passed ? '✅' : '❌'} ${req.name}`);
  });
}

// Run validation if called directly
if (require.main === module) {
  validateMockAPIs()
    .then(() => validateIBKRSpecificRequirements())
    .catch(console.error);
}

module.exports = {
  validateMockAPIs,
  validateIBKRSpecificRequirements,
  IBKR_SCHEMAS
};
