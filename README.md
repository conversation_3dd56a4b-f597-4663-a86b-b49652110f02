# Interactive Brokers Portfolio Dashboard

A professional, privacy-focused portfolio performance dashboard for Interactive Brokers accounts. Built with Next.js 15, TypeScript, and Chart.js.

## 🎯 Features

- **📊 Interactive Performance Charts** - Real-time portfolio performance visualization
- **🔒 Privacy-First Design** - Only percentage returns shown, no monetary amounts
- **📱 Fully Responsive** - Works perfectly on desktop, tablet, and mobile
- **⏱️ Time Period Filtering** - 1D, 1W, 1M, 3M, 6M, 1Y, YTD, ALL
- **🚀 Public Access** - No authentication required for viewing
- **🔄 IBKR API Compatible** - Easy transition from mock to live data

## 🖼️ Dashboard Components

### Performance Metrics

- Total return percentage for selected period
- Win rate, Sharpe ratio, max drawdown
- Profit factor and total trades
- Color-coded performance indicators

### Interactive Charts

- Line chart with percentage-based returns
- Hover tooltips with detailed information
- Responsive design with proper scaling
- Time period synchronization

### Portfolio Analysis

- **Asset Allocation** - Doughnut chart showing portfolio breakdown
- **Sector Analysis** - Progress bars with sector performance
- **Trade History** - Recent trades with percentage returns only

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn

### Installation

```bash
# Clone the repository
git clone https://github.com/TunyatapVich/performance_trading_next.git
cd performance_trading_next

# Install dependencies
npm install

# Start development server
npm run dev
```

Visit `http://localhost:3000/dashboard` to view the dashboard.

### Production Build

```bash
# Build for production
npm run build

# Start production server
npm start
```

## 📡 API Endpoints

Our dashboard uses three main API endpoints that match the Interactive Brokers Client Portal API format:

### `/api/ibkr/performance`

- **Purpose:** Portfolio summary and performance metrics
- **IBKR Equivalent:** `/v1/api/portfolio/{accountId}/summary`
- **Parameters:** `period` (1d, 1w, 1m, 3m, 6m, 1y, ytd, all)

### `/api/ibkr/chart-data`

- **Purpose:** Historical performance data for charts
- **IBKR Equivalent:** `/v1/api/iserver/marketdata/history`
- **Parameters:** `period`, `barSize`

### `/api/ibkr/trades`

- **Purpose:** Trade history with percentage returns
- **IBKR Equivalent:** `/v1/api/iserver/account/trades`

## 🔧 Configuration

### Environment Variables

Create a `.env.local` file:

```env
# IBKR Configuration (for production)
IBKR_BASE_URL=https://localhost:5000/v1/api
IBKR_ACCOUNT_ID=your_account_id

# Dashboard Settings
NEXT_PUBLIC_DASHBOARD_TITLE="Portfolio Performance"
NEXT_PUBLIC_HIDE_MONETARY_AMOUNTS=true
```

### Time Period Configuration

Modify time periods in `src/app/components/TimePeriodFilter.tsx`:

```typescript
export type TimePeriod =
  | "1D"
  | "1W"
  | "1M"
  | "3M"
  | "6M"
  | "1Y"
  | "YTD"
  | "ALL";
```

## 🔄 IBKR Integration

### Current Status: Mock Data

The dashboard currently uses mock data that exactly matches IBKR API format.

### Transitioning to Live IBKR Data

1. **Install IBKR Client Portal Gateway**
2. **Update API endpoints** to point to IBKR
3. **Add authentication** (session cookies)
4. **Configure CORS** for browser requests

See `docs/IBKR_API_MAPPING.md` for detailed integration guide.

### Validation

Verify mock data compatibility:

```bash
node scripts/validate-mock-data.js
```

## 🛡️ Privacy & Security

### Data Privacy Features

- ✅ No account numbers displayed
- ✅ No absolute monetary amounts shown
- ✅ Only percentage-based metrics
- ✅ Position sizes hidden
- ✅ Privacy notices throughout UI

### Security Considerations

- HTTPS required for production
- Session-based authentication with IBKR
- Rate limiting compliance
- CORS configuration for browser security

## 🧪 Testing

### Run Tests

```bash
# Unit tests
npm test

# E2E tests
npm run test:e2e

# API validation
node scripts/validate-mock-data.js
```

### Manual Testing Checklist

- [ ] All time periods work correctly
- [ ] Charts update when period changes
- [ ] Responsive design on mobile/tablet
- [ ] Error handling for failed API calls
- [ ] Loading states display properly
- [ ] No monetary amounts visible

## 📁 Project Structure

```
src/
├── app/
│   ├── dashboard/page.tsx          # Main dashboard page
│   ├── components/                 # React components
│   │   ├── PerformanceChart.tsx    # Chart.js performance chart
│   │   ├── TradingMetrics.tsx      # Metrics grid
│   │   ├── TradeHistory.tsx        # Trade history table
│   │   ├── PortfolioAllocation.tsx # Asset allocation chart
│   │   ├── SectorBreakdown.tsx     # Sector analysis
│   │   └── TimePeriodFilter.tsx    # Time period selector
│   └── api/ibkr/                   # API routes
│       ├── performance/route.ts    # Portfolio metrics
│       ├── chart-data/route.ts     # Chart data
│       └── trades/route.ts         # Trade history
├── docs/
│   └── IBKR_API_MAPPING.md         # API documentation
└── scripts/
    └── validate-mock-data.js       # Validation script
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and validation
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🔗 Links

- [Interactive Brokers API Documentation](https://www.interactivebrokers.com/api/doc.html)
- [Next.js Documentation](https://nextjs.org/docs)
- [Chart.js Documentation](https://www.chartjs.org/docs/)

## 📞 Support

For questions or issues:

- Create an issue on GitHub
- Check the API mapping documentation
- Review the validation script output

---

**Note:** This dashboard is designed for educational and personal use. Ensure compliance with Interactive Brokers' terms of service when using live data.
