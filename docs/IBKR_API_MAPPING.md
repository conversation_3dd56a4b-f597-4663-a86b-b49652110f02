# Interactive Brokers API Mapping Documentation

This document outlines how our dashboard APIs map to the actual Interactive Brokers Client Portal Web API endpoints and data structures.

## Overview

Our dashboard uses three main API endpoints that are designed to match the Interactive Brokers Client Portal API format exactly. This ensures seamless transition from mock data to live IBKR data.

## API Endpoint Mappings

### 1. Portfolio Performance Data

**Our Endpoint:** `/api/ibkr/performance`
**IBKR Endpoint:** `/v1/api/portfolio/{accountId}/summary`

#### Parameters:

- `period` (string): Time period for performance calculation (1d, 1w, 1m, 3m, 6m, 1y, ytd, all)

#### IBKR API Documentation:

- **URL:** `https://www.interactivebrokers.com/api/doc.html#tag/Portfolio/paths/~1portfolio~1%7BaccountId%7D~1summary/get`
- **Description:** Returns portfolio summary including cash, positions, and P&L
- **Authentication:** Requires active session with IBKR Client Portal

#### Response Structure:

```json
{
  "totalCashValue": {
    "amount": 50000.0,
    "currency": "USD"
  },
  "netLiquidationValue": {
    "amount": 57835.5,
    "currency": "USD"
  },
  "unrealizedPnl": {
    "amount": 3245.75,
    "currency": "USD"
  },
  "realizedPnl": {
    "amount": 4589.75,
    "currency": "USD"
  },
  "performanceMetrics": {
    "totalReturnPct": 15.67,
    "dayReturnPct": 0.45,
    "weekReturnPct": 2.1,
    "monthReturnPct": 3.2,
    "quarterReturnPct": 8.7,
    "yearReturnPct": 15.67,
    "ytdReturnPct": 12.4,
    "maxDrawdownPct": -8.3,
    "sharpeRatio": 1.42,
    "winRatePct": 68.5,
    "profitFactor": 1.85,
    "totalTrades": 156
  },
  "period": "1m",
  "lastUpdated": "2025-07-28T12:41:32.262Z"
}
```

**Note:** Monetary amounts are included in the API response but hidden in the UI for privacy.

### 2. Chart Data (Historical Performance)

**Our Endpoint:** `/api/ibkr/chart-data`
**IBKR Endpoint:** `/v1/api/iserver/marketdata/history`

#### Parameters:

- `period` (string): Time period (1d, 1w, 1m, 3m, 6m, 1y, ytd, all)
- `barSize` (string): Bar size for data points (1d, 1h, 5min, etc.)

#### IBKR API Documentation:

- **URL:** `https://www.interactivebrokers.com/api/doc.html#tag/Market-Data/paths/~1iserver~1marketdata~1history/get`
- **Description:** Returns historical market data in OHLC format
- **Authentication:** Requires active session with IBKR Client Portal

#### Response Structure:

```json
{
  "symbol": "PORTFOLIO",
  "text": "Portfolio Performance",
  "data": [
    {
      "t": *************,
      "o": 0,
      "h": 2.87,
      "l": -0.06,
      "c": 2.57,
      "v": 0
    }
  ],
  "period": "1m",
  "barSize": "1d"
}
```

**Field Definitions:**

- `t`: Timestamp in milliseconds (Unix epoch)
- `o`: Open price/percentage
- `h`: High price/percentage
- `l`: Low price/percentage
- `c`: Close price/percentage
- `v`: Volume (set to 0 for portfolio performance)

### 3. Trade History

**Our Endpoint:** `/api/ibkr/trades`
**IBKR Endpoint:** `/v1/api/iserver/account/trades`

#### IBKR API Documentation:

- **URL:** `https://www.interactivebrokers.com/api/doc.html#tag/Trading/paths/~1iserver~1account~1trades/get`
- **Description:** Returns list of trades for the account
- **Authentication:** Requires active session with IBKR Client Portal

#### Response Structure:

```json
[
  {
    "id": "trade_*************_0",
    "symbol": "AAPL",
    "side": "BUY",
    "quantity": 245,
    "returnPct": 3.45,
    "date": "2025-07-28T10:15:32.123Z"
  }
]
```

**Note:** Our implementation shows only percentage returns (`returnPct`) instead of absolute P&L amounts for privacy.

## Authentication & Session Management

### IBKR Client Portal Authentication Flow:

1. **Login:** POST to `/v1/api/iserver/auth/ssodh/init`
2. **Session Validation:** GET `/v1/api/iserver/auth/status`
3. **Keep Alive:** POST `/v1/api/tickle` (every 60 seconds)

### Implementation Notes:

- IBKR requires active browser session with Client Portal
- Session expires after inactivity
- Rate limiting: Max 5 requests per second
- CORS restrictions apply for browser-based requests

## Mock Data Accuracy

Our mock data is designed to match IBKR's exact response format:

### ✅ Accurate Implementations:

- **Timestamp Format:** Unix milliseconds (matches IBKR)
- **OHLC Structure:** Exact field names (t, o, h, l, c, v)
- **Currency Objects:** `{amount, currency}` structure
- **Field Naming:** camelCase matching IBKR convention
- **Data Types:** Numbers for percentages, strings for IDs

### 🔄 Transition to Live Data:

To switch from mock to live IBKR data:

1. **Replace fetch URLs:**

   ```typescript
   // From:
   const response = await fetch("/api/ibkr/performance");

   // To:
   const response = await fetch(
     "https://localhost:5000/v1/api/portfolio/{accountId}/summary"
   );
   ```

2. **Add authentication headers:**

   ```typescript
   const response = await fetch(url, {
     credentials: "include", // Include session cookies
     headers: {
       "Content-Type": "application/json",
     },
   });
   ```

3. **Handle IBKR-specific error responses:**
   ```typescript
   if (response.status === 401) {
     // Redirect to IBKR login
   }
   ```

## Security Considerations

### Data Privacy:

- ✅ No account numbers exposed
- ✅ No absolute monetary amounts in UI
- ✅ Only percentage-based metrics shown
- ✅ Position sizes hidden

### IBKR Security Requirements:

- Must use HTTPS in production
- Session cookies required for authentication
- IP whitelisting may be required
- Two-factor authentication supported

## Testing & Validation

### Mock Data Validation:

- Response structures match IBKR documentation
- Field types and naming conventions correct
- Realistic data ranges and relationships
- Proper error response formats

### Live Data Testing Checklist:

- [ ] Authentication flow working
- [ ] Session management implemented
- [ ] Error handling for expired sessions
- [ ] Rate limiting respected
- [ ] CORS configuration correct
- [ ] Data transformation working
- [ ] Privacy filters applied

## Validation Results

✅ **Mock Data Validation Passed** (Last checked: 2025-07-28)

Our validation script confirms:

- All API responses match IBKR schema exactly
- Timestamp formats are correct (Unix milliseconds)
- Currency objects follow IBKR structure
- OHLC data uses proper field naming (t,o,h,l,c,v)
- All required fields are present
- Data types match IBKR specifications

Run validation: `node scripts/validate-mock-data.js`

## Implementation Files

### API Routes:

- `src/app/api/ibkr/performance/route.ts` - Portfolio summary endpoint
- `src/app/api/ibkr/chart-data/route.ts` - Historical chart data endpoint
- `src/app/api/ibkr/trades/route.ts` - Trade history endpoint

### Dashboard Components:

- `src/app/dashboard/page.tsx` - Main dashboard page
- `src/app/components/PerformanceChart.tsx` - Chart.js performance chart
- `src/app/components/TradingMetrics.tsx` - Performance metrics grid
- `src/app/components/TradeHistory.tsx` - Trade history table
- `src/app/components/PortfolioAllocation.tsx` - Asset allocation chart
- `src/app/components/SectorBreakdown.tsx` - Sector breakdown visualization
- `src/app/components/TimePeriodFilter.tsx` - Time period selector

### Validation:

- `scripts/validate-mock-data.js` - API validation script
- `docs/IBKR_API_MAPPING.md` - This documentation

## Quick Start for IBKR Integration

1. **Set up IBKR Client Portal:**

   ```bash
   # Download and run IBKR Client Portal Gateway
   # Default URL: https://localhost:5000
   ```

2. **Update API base URL:**

   ```typescript
   const IBKR_BASE_URL = "https://localhost:5000/v1/api";
   ```

3. **Add authentication:**

   ```typescript
   // Include session cookies in requests
   fetch(url, { credentials: "include" });
   ```

4. **Test connection:**
   ```bash
   curl -k https://localhost:5000/v1/api/iserver/auth/status
   ```

## References

- [IBKR Client Portal Web API Documentation](https://www.interactivebrokers.com/api/doc.html)
- [IBKR Authentication Guide](https://www.interactivebrokers.com/api/doc.html#tag/Authentication)
- [IBKR Rate Limiting](https://www.interactivebrokers.com/api/doc.html#section/Rate-Limiting)
- [IBKR Client Portal Gateway Setup](https://www.interactivebrokers.com/api/doc.html#tag/Getting-Started)
