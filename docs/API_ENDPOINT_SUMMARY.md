# API Endpoint Summary

## Quick Reference: Dashboard APIs → IBKR APIs

| Dashboard Endpoint      | IBKR Endpoint                           | Purpose           | Status         |
| ----------------------- | --------------------------------------- | ----------------- | -------------- |
| `/api/ibkr/performance` | `/v1/api/portfolio/{accountId}/summary` | Portfolio metrics | ✅ Mock Ready  |
| `/api/ibkr/chart-data`  | `/v1/api/iserver/marketdata/history`    | Historical data   | ✅ Mock Ready  |
| ~~`/api/ibkr/trades`~~  | ~~`/v1/api/iserver/account/trades`~~    | ~~Trade history~~ | ❌ **REMOVED** |

## 1. Portfolio Performance Data

### Our Implementation

```
GET /api/ibkr/performance?period=1m
```

### IBKR Equivalent

```
GET /v1/api/portfolio/{accountId}/summary
```

**Response Structure (Validated ✅):**

```json
{
  "totalCashValue": { "amount": 50000.0, "currency": "USD" },
  "netLiquidationValue": { "amount": 57835.5, "currency": "USD" },
  "unrealizedPnl": { "amount": 3245.75, "currency": "USD" },
  "realizedPnl": { "amount": 4589.75, "currency": "USD" },
  "performanceMetrics": {
    "totalReturnPct": 15.67,
    "dayReturnPct": 0.45,
    "weekReturnPct": 2.1,
    "monthReturnPct": 3.2,
    "quarterReturnPct": 8.7,
    "yearReturnPct": 15.67,
    "ytdReturnPct": 12.4,
    "maxDrawdownPct": -8.3,
    "sharpeRatio": 1.42,
    "winRatePct": 68.5,
    "profitFactor": 1.85,
    "totalTrades": 156
  },
  "period": "1m",
  "lastUpdated": "2025-07-28T12:41:32.262Z"
}
```

## 2. Chart Data (Historical Performance)

### Our Implementation

```
GET /api/ibkr/chart-data?period=1w&barSize=1d
```

### IBKR Equivalent

```
GET /v1/api/iserver/marketdata/history?conid={contractId}&period=1w&bar=1d
```

**Response Structure (Validated ✅):**

```json
{
  "symbol": "PORTFOLIO",
  "text": "Portfolio Performance",
  "data": [
    {
      "t": 1753188103664, // Unix timestamp (milliseconds)
      "o": 0, // Open percentage
      "h": 2.87, // High percentage
      "l": -0.06, // Low percentage
      "c": 2.57, // Close percentage
      "v": 0 // Volume (0 for portfolio)
    }
  ],
  "period": "1w",
  "barSize": "1d"
}
```

## 3. Trade History (REMOVED)

**Status:** ❌ **REMOVED FOR PRIVACY**

### Reason for Removal

Trade history contains strategic trading information that should not be displayed publicly:

- Individual trade details reveal trading patterns
- Position sizes expose portfolio strategy
- Entry/exit points show market timing decisions

### Previously Available

```
GET /api/ibkr/trades (REMOVED)
```

**Note:** Only aggregated performance metrics are now displayed to protect trading strategies.

## Authentication Flow (For Live IBKR Integration)

### 1. Session Initialization

```
POST /v1/api/iserver/auth/ssodh/init
```

### 2. Authentication Status Check

```
GET /v1/api/iserver/auth/status
```

### 3. Keep Session Alive

```
POST /v1/api/tickle
```

## Data Privacy Implementation

### Hidden from UI (Present in API Response)

- `totalCashValue.amount` - Account cash balance
- `netLiquidationValue.amount` - Total account value
- `unrealizedPnl.amount` - Unrealized profit/loss
- `realizedPnl.amount` - Realized profit/loss

### Shown in UI (Privacy-Safe)

- All `*ReturnPct` fields - Percentage returns only
- `sharpeRatio` - Risk-adjusted return metric
- `winRatePct` - Win rate percentage
- `profitFactor` - Profit factor ratio
- `totalTrades` - Number of trades
- `maxDrawdownPct` - Maximum drawdown percentage

## Validation Status

✅ **All APIs Validated** (2025-07-28)

Run validation:

```bash
node scripts/validate-mock-data.js
```

**Validation Results:**

- ✅ Response structures match IBKR schema exactly
- ✅ Timestamp formats correct (Unix milliseconds)
- ✅ Currency objects follow IBKR structure
- ✅ OHLC data uses proper field naming (t,o,h,l,c,v)
- ✅ All required fields present
- ✅ Data types match IBKR specifications

## Transition Checklist

### To Switch from Mock to Live IBKR Data:

1. **Setup IBKR Client Portal Gateway**

   - Download from IBKR website
   - Run on `https://localhost:5000`
   - Configure SSL certificates

2. **Update API Base URLs**

   ```typescript
   // From:
   const response = await fetch("/api/ibkr/performance");

   // To:
   const response = await fetch(
     "https://localhost:5000/v1/api/portfolio/{accountId}/summary"
   );
   ```

3. **Add Authentication**

   ```typescript
   const response = await fetch(url, {
     credentials: "include", // Include session cookies
     headers: {
       "Content-Type": "application/json",
     },
   });
   ```

4. **Handle IBKR Error Responses**

   ```typescript
   if (response.status === 401) {
     // Redirect to IBKR login
     window.location.href = "https://localhost:5000";
   }
   ```

5. **Configure CORS**
   - Allow `https://localhost:5000` in CORS settings
   - Handle preflight requests
   - Include credentials in requests

## Rate Limiting

**IBKR Limits:**

- Maximum 5 requests per second
- Session expires after 24 hours of inactivity
- Keep-alive required every 60 seconds

**Our Implementation:**

- Automatic retry with exponential backoff
- Error handling for rate limit responses
- Session management with keep-alive

## Security Considerations

### Production Requirements:

- ✅ HTTPS only (no HTTP)
- ✅ Session-based authentication
- ✅ No API keys in client-side code
- ✅ CORS properly configured
- ✅ Rate limiting respected
- ✅ Error handling for expired sessions

### Privacy Features:

- ✅ No account numbers displayed
- ✅ No absolute monetary amounts in UI
- ✅ Only percentage-based metrics shown
- ✅ Position sizes hidden
- ✅ Privacy notices throughout interface

---

**Ready for Production:** All mock APIs are IBKR-compatible and validated. The dashboard can be transitioned to live IBKR data by following the checklist above.
