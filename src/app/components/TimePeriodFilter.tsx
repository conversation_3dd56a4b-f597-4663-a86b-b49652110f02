'use client'

import { useState } from 'react'

export type TimePeriod = '1D' | '1W' | '1M' | '3M' | '6M' | '1Y' | 'YTD' | 'ALL'

interface TimePeriodFilterProps {
  selectedPeriod: TimePeriod
  onPeriodChange: (period: TimePeriod) => void
  className?: string
}

const TIME_PERIODS: { value: TimePeriod; label: string; description: string }[] = [
  { value: '1D', label: '1D', description: '1 Day' },
  { value: '1W', label: '1W', description: '1 Week' },
  { value: '1M', label: '1M', description: '1 Month' },
  { value: '3M', label: '3M', description: '3 Months' },
  { value: '6M', label: '6M', description: '6 Months' },
  { value: '1Y', label: '1Y', description: '1 Year' },
  { value: 'YTD', label: 'YTD', description: 'Year to Date' },
  { value: 'ALL', label: 'ALL', description: 'All Time' }
]

export function TimePeriodFilter({ selectedPeriod, onPeriodChange, className = '' }: TimePeriodFilterProps) {
  return (
    <div className={`flex flex-wrap gap-2 ${className}`}>
      {TIME_PERIODS.map((period) => (
        <button
          key={period.value}
          onClick={() => onPeriodChange(period.value)}
          className={`
            px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200
            ${selectedPeriod === period.value
              ? 'bg-blue-600 text-white shadow-md'
              : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:border-gray-400'
            }
          `}
          title={period.description}
        >
          {period.label}
        </button>
      ))}
    </div>
  )
}

// Utility function to get date range based on period
export function getDateRangeForPeriod(period: TimePeriod): { startDate: Date; endDate: Date } {
  const endDate = new Date()
  const startDate = new Date()

  switch (period) {
    case '1D':
      startDate.setDate(endDate.getDate() - 1)
      break
    case '1W':
      startDate.setDate(endDate.getDate() - 7)
      break
    case '1M':
      startDate.setMonth(endDate.getMonth() - 1)
      break
    case '3M':
      startDate.setMonth(endDate.getMonth() - 3)
      break
    case '6M':
      startDate.setMonth(endDate.getMonth() - 6)
      break
    case '1Y':
      startDate.setFullYear(endDate.getFullYear() - 1)
      break
    case 'YTD':
      startDate.setMonth(0, 1) // January 1st of current year
      break
    case 'ALL':
      startDate.setFullYear(2020, 0, 1) // Default to 2020 for "all time"
      break
    default:
      startDate.setMonth(endDate.getMonth() - 1)
  }

  return { startDate, endDate }
}

// Utility function to format period for API calls
export function formatPeriodForAPI(period: TimePeriod): string {
  const periodMap: Record<TimePeriod, string> = {
    '1D': '1d',
    '1W': '1w',
    '1M': '1m',
    '3M': '3m',
    '6M': '6m',
    '1Y': '1y',
    'YTD': 'ytd',
    'ALL': 'all'
  }
  
  return periodMap[period] || '1m'
}
