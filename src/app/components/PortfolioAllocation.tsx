'use client'

import { useState, useEffect } from 'react'
import { Chart as ChartJS, ArcElement, Too<PERSON><PERSON>, Legend } from 'chart.js'
import { Doughnut } from 'react-chartjs-2'

ChartJS.register(ArcElement, Tooltip, Legend)

interface AllocationData {
  assetClass: string
  percentage: number
  color: string
}

interface PortfolioAllocationProps {
  className?: string
}

export function PortfolioAllocation({ className = '' }: PortfolioAllocationProps) {
  const [allocationData, setAllocationData] = useState<AllocationData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchAllocationData()
  }, [])

  const fetchAllocationData = async () => {
    setLoading(true)
    try {
      // Mock data - replace with actual IBKR API calls
      const mockData: AllocationData[] = [
        { assetClass: 'Stocks', percentage: 65.5, color: '#3B82F6' },
        { assetClass: 'ETFs', percentage: 20.3, color: '#10B981' },
        { assetClass: 'Options', percentage: 8.7, color: '#F59E0B' },
        { assetClass: 'Bonds', percentage: 3.2, color: '#8B5CF6' },
        { assetClass: 'Cash', percentage: 2.3, color: '#6B7280' }
      ]
      setAllocationData(mockData)
    } catch (error) {
      console.error('Failed to fetch allocation data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
        <h3 className="text-lg font-semibold mb-4">Portfolio Allocation</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="text-gray-500">Loading allocation data...</div>
        </div>
      </div>
    )
  }

  const chartData = {
    labels: allocationData.map(item => item.assetClass),
    datasets: [
      {
        data: allocationData.map(item => item.percentage),
        backgroundColor: allocationData.map(item => item.color),
        borderColor: allocationData.map(item => item.color),
        borderWidth: 2,
        hoverBorderWidth: 3
      }
    ]
  }

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        callbacks: {
          label: function(context: any) {
            return `${context.label}: ${context.parsed}%`
          }
        }
      }
    },
    cutout: '60%'
  }

  return (
    <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
      <h3 className="text-lg font-semibold mb-6">Portfolio Allocation</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Chart */}
        <div className="h-64 relative">
          <Doughnut data={chartData} options={chartOptions} />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">100%</div>
              <div className="text-sm text-gray-500">Allocated</div>
            </div>
          </div>
        </div>
        
        {/* Legend */}
        <div className="space-y-3">
          {allocationData.map((item, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center">
                <div 
                  className="w-4 h-4 rounded-full mr-3"
                  style={{ backgroundColor: item.color }}
                ></div>
                <span className="text-sm font-medium text-gray-700">{item.assetClass}</span>
              </div>
              <span className="text-sm font-bold text-gray-900">{item.percentage}%</span>
            </div>
          ))}
        </div>
      </div>
      
      <div className="mt-6 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-blue-800">
              Allocation Information
            </h4>
            <div className="mt-1 text-sm text-blue-700">
              <p>
                Portfolio allocation shown as percentages only. Actual position sizes and monetary values are hidden for privacy.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
