'use client'

import { useState, useEffect } from 'react'

interface Trade {
  id: string
  symbol: string
  side: 'BUY' | 'SELL'
  quantity: number
  price: number
  pnl: number
  date: string
}

export function TradeHistory() {
  const [trades, setTrades] = useState<Trade[]>([])

  useEffect(() => {
    fetchTrades()
  }, [])

  const fetchTrades = async () => {
    try {
      const response = await fetch('/api/ibkr/trades')
      const data = await response.json()
      setTrades(data)
    } catch (error) {
      console.error('Failed to fetch trades:', error)
    }
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Recent Trades</h3>
      <div className="overflow-x-auto">
        <table className="min-w-full">
          <thead>
            <tr className="border-b">
              <th className="text-left py-2">Symbol</th>
              <th className="text-left py-2">Side</th>
              <th className="text-left py-2">Qty</th>
              <th className="text-left py-2">Price</th>
              <th className="text-left py-2">P&L</th>
            </tr>
          </thead>
          <tbody>
            {trades.slice(0, 10).map((trade) => (
              <tr key={trade.id} className="border-b">
                <td className="py-2">{trade.symbol}</td>
                <td className="py-2">
                  <span className={`px-2 py-1 rounded text-xs ${
                    trade.side === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {trade.side}
                  </span>
                </td>
                <td className="py-2">{trade.quantity}</td>
                <td className="py-2">${trade.price.toFixed(2)}</td>
                <td className={`py-2 ${trade.pnl >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${trade.pnl.toFixed(2)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  )
}