'use client'

import { useState, useEffect } from 'react'
import { format } from 'date-fns'

interface Trade {
  id: string
  symbol: string
  side: 'BUY' | 'SELL'
  quantity: number
  returnPct: number // Changed from price to return percentage
  date: string
}

interface TradeHistoryProps {
  className?: string
}

export function TradeHistory({ className = '' }: TradeHistoryProps) {
  const [trades, setTrades] = useState<Trade[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchTrades()
  }, [])

  const fetchTrades = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/ibkr/trades')
      const data = await response.json()
      setTrades(data)
    } catch (error) {
      console.error('Failed to fetch trades:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
        <h3 className="text-lg font-semibold mb-4">Recent Trades</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="text-gray-500">Loading trade history...</div>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Recent Trades</h3>
        <div className="text-sm text-gray-500">
          Last {trades.length} trades
        </div>
      </div>

      {trades.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          No trades available
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-2 font-medium text-gray-700">Symbol</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Side</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Quantity</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Return %</th>
                <th className="text-left py-3 px-2 font-medium text-gray-700">Date</th>
              </tr>
            </thead>
            <tbody>
              {trades.slice(0, 10).map((trade) => (
                <tr key={trade.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-2">
                    <span className="font-medium text-gray-900">{trade.symbol}</span>
                  </td>
                  <td className="py-3 px-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${trade.side === 'BUY'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                      }`}>
                      {trade.side}
                    </span>
                  </td>
                  <td className="py-3 px-2 text-gray-700">
                    {trade.quantity.toLocaleString()}
                  </td>
                  <td className={`py-3 px-2 font-medium ${trade.returnPct >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                    {trade.returnPct >= 0 ? '+' : ''}{trade.returnPct.toFixed(2)}%
                  </td>
                  <td className="py-3 px-2 text-gray-500 text-sm">
                    {format(new Date(trade.date), 'MMM dd, HH:mm')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-yellow-800">
              Privacy Notice
            </h4>
            <div className="mt-1 text-sm text-yellow-700">
              <p>
                Trade data shows percentage returns only. Actual prices, monetary amounts, and position sizes are hidden for privacy and security.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}