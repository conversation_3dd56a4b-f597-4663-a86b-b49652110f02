import { TimePeriod } from './TimePeriodFilter'

interface PerformanceMetrics {
  totalReturnPct: number
  dayReturnPct: number
  weekReturnPct: number
  monthReturnPct: number
  quarterReturnPct: number
  yearReturnPct: number
  ytdReturnPct: number
  maxDrawdownPct: number
  sharpeRatio: number
  winRatePct: number
  profitFactor: number
  totalTrades: number
}

interface TradingMetricsProps {
  data: {
    performanceMetrics: PerformanceMetrics
    period: string
    lastUpdated: string
  }
  selectedPeriod: TimePeriod
}

export function TradingMetrics({ data, selectedPeriod }: TradingMetricsProps) {
  // Get the appropriate return based on selected period
  const getPeriodReturn = (period: TimePeriod): number => {
    switch (period) {
      case '1D':
        return data.performanceMetrics.dayReturnPct
      case '1W':
        return data.performanceMetrics.weekReturnPct
      case '1M':
        return data.performanceMetrics.monthReturnPct
      case '3M':
        return data.performanceMetrics.quarterReturnPct
      case '6M':
        return data.performanceMetrics.quarterReturnPct * 2 // Approximate
      case '1Y':
        return data.performanceMetrics.yearReturnPct
      case 'YTD':
        return data.performanceMetrics.ytdReturnPct
      case 'ALL':
        return data.performanceMetrics.totalReturnPct
      default:
        return data.performanceMetrics.monthReturnPct
    }
  }

  const periodReturn = getPeriodReturn(selectedPeriod)

  const metrics = [
    {
      label: `${selectedPeriod} Return`,
      value: `${periodReturn >= 0 ? '+' : ''}${periodReturn.toFixed(2)}%`,
      color: periodReturn >= 0 ? 'text-green-600' : 'text-red-600',
      description: 'Portfolio return for selected period'
    },
    {
      label: 'Win Rate',
      value: `${data.performanceMetrics.winRatePct.toFixed(1)}%`,
      color: 'text-blue-600',
      description: 'Percentage of profitable trades'
    },
    {
      label: 'Sharpe Ratio',
      value: data.performanceMetrics.sharpeRatio.toFixed(2),
      color: data.performanceMetrics.sharpeRatio >= 1 ? 'text-green-600' : 'text-yellow-600',
      description: 'Risk-adjusted return measure'
    },
    {
      label: 'Max Drawdown',
      value: `${data.performanceMetrics.maxDrawdownPct.toFixed(2)}%`,
      color: 'text-red-600',
      description: 'Largest peak-to-trough decline'
    },
    {
      label: 'Total Trades',
      value: data.performanceMetrics.totalTrades.toString(),
      color: 'text-gray-600',
      description: 'Number of completed trades'
    },
    {
      label: 'Profit Factor',
      value: data.performanceMetrics.profitFactor.toFixed(2),
      color: data.performanceMetrics.profitFactor >= 1 ? 'text-green-600' : 'text-red-600',
      description: 'Ratio of gross profit to gross loss'
    }
  ]

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-900">Performance Metrics</h2>
        <div className="text-sm text-gray-500">
          Last updated: {new Date(data.lastUpdated).toLocaleString()}
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {metrics.map((metric) => (
          <div key={metric.label} className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
            <div className="text-sm font-medium text-gray-500 mb-1">{metric.label}</div>
            <div className={`text-2xl font-bold ${metric.color} mb-1`}>{metric.value}</div>
            <div className="text-xs text-gray-400">{metric.description}</div>
          </div>
        ))}
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              Performance Data Notice
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                All performance metrics are displayed as percentages only. No absolute monetary amounts or account balances are shown to maintain privacy and security.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}