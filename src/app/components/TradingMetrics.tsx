interface TradingMetricsProps {
  data: {
    totalReturn: number
    winRate: number
    sharpeRatio: number
    maxDrawdown: number
    totalTrades: number
    profitFactor: number
  }
}

export function TradingMetrics({ data }: TradingMetricsProps) {
  const metrics = [
    {
      label: 'Total Return',
      value: `${data.totalReturn.toFixed(2)}%`,
      color: data.totalReturn >= 0 ? 'text-green-600' : 'text-red-600'
    },
    {
      label: 'Win Rate',
      value: `${data.winRate.toFixed(1)}%`,
      color: 'text-blue-600'
    },
    {
      label: 'Sharpe Ratio',
      value: data.sharpeRatio.toFixed(2),
      color: 'text-purple-600'
    },
    {
      label: 'Max Drawdown',
      value: `${data.maxDrawdown.toFixed(2)}%`,
      color: 'text-red-600'
    },
    {
      label: 'Total Trades',
      value: data.totalTrades.toString(),
      color: 'text-gray-600'
    },
    {
      label: 'Profit Factor',
      value: data.profitFactor.toFixed(2),
      color: 'text-green-600'
    }
  ]

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
      {metrics.map((metric) => (
        <div key={metric.label} className="bg-white p-6 rounded-lg shadow">
          <div className="text-sm font-medium text-gray-500">{metric.label}</div>
          <div className={`text-2xl font-bold ${metric.color}`}>{metric.value}</div>
        </div>
      ))}
    </div>
  )
}