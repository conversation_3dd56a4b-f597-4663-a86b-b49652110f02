'use client'

import { useState, useEffect } from 'react'

interface SectorData {
  sector: string
  percentage: number
  returnPct: number
  color: string
}

interface SectorBreakdownProps {
  className?: string
}

export function SectorBreakdown({ className = '' }: SectorBreakdownProps) {
  const [sectorData, setSectorData] = useState<SectorData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchSectorData()
  }, [])

  const fetchSectorData = async () => {
    setLoading(true)
    try {
      // Mock data - replace with actual IBKR API calls
      const mockData: SectorData[] = [
        { sector: 'Technology', percentage: 35.2, returnPct: 18.5, color: '#3B82F6' },
        { sector: 'Healthcare', percentage: 18.7, returnPct: 12.3, color: '#10B981' },
        { sector: 'Financial Services', percentage: 15.4, returnPct: 8.9, color: '#F59E0B' },
        { sector: 'Consumer Cyclical', percentage: 12.1, returnPct: 15.7, color: '#EF4444' },
        { sector: 'Communication', percentage: 8.9, returnPct: 6.2, color: '#8B5CF6' },
        { sector: 'Energy', percentage: 5.3, returnPct: 22.1, color: '#F97316' },
        { sector: 'Industrials', percentage: 4.4, returnPct: 9.8, color: '#06B6D4' }
      ]
      setSectorData(mockData)
    } catch (error) {
      console.error('Failed to fetch sector data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
        <h3 className="text-lg font-semibold mb-4">Sector Breakdown</h3>
        <div className="h-64 flex items-center justify-center">
          <div className="text-gray-500">Loading sector data...</div>
        </div>
      </div>
    )
  }

  const maxPercentage = Math.max(...sectorData.map(s => s.percentage))

  return (
    <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold">Sector Breakdown</h3>
        <div className="text-sm text-gray-500">
          {sectorData.length} sectors
        </div>
      </div>
      
      <div className="space-y-4">
        {sectorData.map((sector, index) => (
          <div key={index} className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-3"
                  style={{ backgroundColor: sector.color }}
                ></div>
                <span className="text-sm font-medium text-gray-700">{sector.sector}</span>
              </div>
              <div className="text-right">
                <div className="text-sm font-bold text-gray-900">{sector.percentage}%</div>
                <div className={`text-xs ${sector.returnPct >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {sector.returnPct >= 0 ? '+' : ''}{sector.returnPct.toFixed(1)}%
                </div>
              </div>
            </div>
            
            {/* Progress bar */}
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="h-2 rounded-full transition-all duration-300"
                style={{ 
                  width: `${(sector.percentage / maxPercentage) * 100}%`,
                  backgroundColor: sector.color
                }}
              ></div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-6 grid grid-cols-2 gap-4 pt-4 border-t border-gray-200">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {sectorData.reduce((sum, s) => sum + s.percentage, 0).toFixed(1)}%
          </div>
          <div className="text-sm text-gray-500">Total Allocation</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600">
            +{(sectorData.reduce((sum, s) => sum + (s.returnPct * s.percentage / 100), 0)).toFixed(1)}%
          </div>
          <div className="text-sm text-gray-500">Weighted Return</div>
        </div>
      </div>
      
      <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h4 className="text-sm font-medium text-green-800">
              Sector Performance
            </h4>
            <div className="mt-1 text-sm text-green-700">
              <p>
                Sector allocation and returns shown as percentages. Individual position details are hidden for privacy.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
