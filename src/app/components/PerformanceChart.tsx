'use client'

import { useState, useEffect, useRef } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler,
  TooltipItem
} from 'chart.js'
import { Line } from 'react-chartjs-2'
import { format } from 'date-fns'
import { TimePeriod } from './TimePeriodFilter'

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

interface IBKRChartDataPoint {
  t: number // timestamp in milliseconds
  o: number // open percentage
  h: number // high percentage
  l: number // low percentage
  c: number // close percentage
  v: number // volume (not used for portfolio)
}

interface IBKRChartResponse {
  symbol: string
  text: string
  data: IBKRChartDataPoint[]
  period: string
  barSize: string
}

interface PerformanceChartProps {
  selectedPeriod: TimePeriod
  className?: string
}

export function PerformanceChart({ selectedPeriod, className = '' }: PerformanceChartProps) {
  const [chartData, setChartData] = useState<IBKRChartResponse | null>(null)
  const [loading, setLoading] = useState(true)
  const chartRef = useRef<ChartJS<'line', number[], string>>(null)

  useEffect(() => {
    fetchChartData(selectedPeriod)
  }, [selectedPeriod])

  const fetchChartData = async (period: TimePeriod) => {
    setLoading(true)
    try {
      const periodParam = period.toLowerCase()
      const response = await fetch(`/api/ibkr/chart-data?period=${periodParam}&barSize=1d`)
      const data: IBKRChartResponse = await response.json()
      setChartData(data)
    } catch (error) {
      console.error('Failed to fetch chart data:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatDateLabel = (timestamp: number, period: TimePeriod): string => {
    const date = new Date(timestamp)

    switch (period) {
      case '1D':
        return format(date, 'HH:mm')
      case '1W':
      case '1M':
        return format(date, 'MMM dd')
      case '3M':
      case '6M':
        return format(date, 'MMM dd')
      case '1Y':
      case 'YTD':
      case 'ALL':
        return format(date, 'MMM yyyy')
      default:
        return format(date, 'MMM dd')
    }
  }

  if (loading) {
    return (
      <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
        <h3 className="text-lg font-semibold mb-4">Portfolio Performance</h3>
        <div className="h-80 flex items-center justify-center">
          <div className="text-gray-500">Loading chart data...</div>
        </div>
      </div>
    )
  }

  if (!chartData || !chartData.data.length) {
    return (
      <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
        <h3 className="text-lg font-semibold mb-4">Portfolio Performance</h3>
        <div className="h-80 flex items-center justify-center">
          <div className="text-gray-500">No data available</div>
        </div>
      </div>
    )
  }

  const labels = chartData.data.map(point => formatDateLabel(point.t, selectedPeriod))
  const dataPoints = chartData.data.map(point => point.c) // Use close prices (cumulative returns)

  const currentReturn = dataPoints[dataPoints.length - 1] || 0
  const isPositive = currentReturn >= 0

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.1)',
        borderWidth: 1,
        callbacks: {
          title: (context: TooltipItem<'line'>[]) => {
            const dataIndex = context[0].dataIndex
            const timestamp = chartData.data[dataIndex].t
            return format(new Date(timestamp), 'MMM dd, yyyy HH:mm')
          },
          label: (context: TooltipItem<'line'>) => {
            const value = context.parsed.y
            return `Return: ${value >= 0 ? '+' : ''}${value.toFixed(2)}%`
          }
        }
      }
    },
    scales: {
      x: {
        display: true,
        grid: {
          display: false
        },
        ticks: {
          maxTicksLimit: 8,
          color: '#6B7280'
        }
      },
      y: {
        display: true,
        grid: {
          color: 'rgba(0, 0, 0, 0.1)'
        },
        ticks: {
          color: '#6B7280',
          callback: function (value: any) {
            return `${value}%`
          }
        }
      }
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false
    },
    elements: {
      point: {
        radius: 0,
        hoverRadius: 6,
        hoverBorderWidth: 2
      },
      line: {
        borderWidth: 2,
        tension: 0.1
      }
    }
  }

  const data = {
    labels,
    datasets: [
      {
        label: 'Portfolio Return',
        data: dataPoints,
        borderColor: isPositive ? '#10B981' : '#EF4444',
        backgroundColor: isPositive
          ? 'rgba(16, 185, 129, 0.1)'
          : 'rgba(239, 68, 68, 0.1)',
        fill: true,
        pointBackgroundColor: isPositive ? '#10B981' : '#EF4444',
        pointBorderColor: '#ffffff',
        pointHoverBackgroundColor: isPositive ? '#059669' : '#DC2626',
        pointHoverBorderColor: '#ffffff'
      }
    ]
  }

  return (
    <div className={`bg-white p-6 rounded-lg shadow ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Portfolio Performance</h3>
        <div className="text-right">
          <div className={`text-2xl font-bold ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {currentReturn >= 0 ? '+' : ''}{currentReturn.toFixed(2)}%
          </div>
          <div className="text-sm text-gray-500">{selectedPeriod} Return</div>
        </div>
      </div>
      <div className="h-80">
        <Line ref={chartRef} data={data} options={chartOptions} />
      </div>
    </div>
  )
}