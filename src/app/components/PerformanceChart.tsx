'use client'

import { useState, useEffect } from 'react'

interface ChartData {
  date: string
  value: number
  cumulative: number
}

export function PerformanceChart() {
  const [chartData, setChartData] = useState<ChartData[]>([])

  useEffect(() => {
    fetchChartData()
  }, [])

  const fetchChartData = async () => {
    try {
      const response = await fetch('/api/ibkr/chart-data')
      const data = await response.json()
      setChartData(data)
    } catch (error) {
      console.error('Failed to fetch chart data:', error)
    }
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <h3 className="text-lg font-semibold mb-4">Performance Over Time</h3>
      <div className="h-64 flex items-end space-x-1">
        {chartData.map((point, index) => (
          <div
            key={index}
            className="bg-blue-500 w-2 rounded-t"
            style={{
              height: `${Math.max(5, (point.cumulative / Math.max(...chartData.map(d => d.cumulative))) * 100)}%`
            }}
            title={`${point.date}: ${point.cumulative.toFixed(2)}%`}
          />
        ))}
      </div>
    </div>
  )
}