import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Mock data - replace with actual IBKR API calls
    const performanceData = {
      totalReturn: 15.67,
      winRate: 68.5,
      sharpeRatio: 1.42,
      maxDrawdown: -8.3,
      totalTrades: 156,
      profitFactor: 1.85
    }

    return NextResponse.json(performanceData)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch performance data' },
      { status: 500 }
    )
  }
}