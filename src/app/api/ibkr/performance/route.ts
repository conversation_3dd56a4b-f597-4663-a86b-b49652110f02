import { NextResponse } from 'next/server'

// IBKR API compatible response structure
interface IBKRPerformanceResponse {
  // Portfolio summary data matching IBKR /portfolio/{accountId}/summary format
  totalCashValue: {
    amount: number
    currency: string
  }
  netLiquidationValue: {
    amount: number
    currency: string
  }
  unrealizedPnl: {
    amount: number
    currency: string
  }
  realizedPnl: {
    amount: number
    currency: string
  }
  // Performance metrics as percentages only
  performanceMetrics: {
    totalReturnPct: number
    dayReturnPct: number
    weekReturnPct: number
    monthReturnPct: number
    quarterReturnPct: number
    yearReturnPct: number
    ytdReturnPct: number
    maxDrawdownPct: number
    sharpeRatio: number
    winRatePct: number
    profitFactor: number
    totalTrades: number
  }
  // Time period data
  period: string
  lastUpdated: string
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '1m'

    // Mock data matching IBKR API structure - replace with actual IBKR API calls
    // This structure matches the real IBKR Client Portal API response format
    const performanceData: IBKRPerformanceResponse = {
      totalCashValue: {
        amount: 50000.00, // Hidden in UI - only for internal calculations
        currency: 'USD'
      },
      netLiquidationValue: {
        amount: 57835.50, // Hidden in UI - only for internal calculations
        currency: 'USD'
      },
      unrealizedPnl: {
        amount: 3245.75, // Hidden in UI - only for internal calculations
        currency: 'USD'
      },
      realizedPnl: {
        amount: 4589.75, // Hidden in UI - only for internal calculations
        currency: 'USD'
      },
      performanceMetrics: {
        totalReturnPct: getPeriodReturn(period, 'total'),
        dayReturnPct: getPeriodReturn(period, '1d'),
        weekReturnPct: getPeriodReturn(period, '1w'),
        monthReturnPct: getPeriodReturn(period, '1m'),
        quarterReturnPct: getPeriodReturn(period, '3m'),
        yearReturnPct: getPeriodReturn(period, '1y'),
        ytdReturnPct: getPeriodReturn(period, 'ytd'),
        maxDrawdownPct: -8.3,
        sharpeRatio: 1.42,
        winRatePct: 68.5,
        profitFactor: 1.85,
        totalTrades: 156
      },
      period: period,
      lastUpdated: new Date().toISOString()
    }

    return NextResponse.json(performanceData)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch performance data' },
      { status: 500 }
    )
  }
}

// Helper function to generate period-specific returns
function getPeriodReturn(period: string, returnType: string): number {
  const baseReturns: Record<string, Record<string, number>> = {
    '1d': { '1d': 0.45, '1w': 2.1, '1m': 3.2, '3m': 8.7, '1y': 15.67, 'ytd': 12.4, 'total': 15.67 },
    '1w': { '1d': 0.45, '1w': 2.1, '1m': 3.2, '3m': 8.7, '1y': 15.67, 'ytd': 12.4, 'total': 15.67 },
    '1m': { '1d': 0.45, '1w': 2.1, '1m': 3.2, '3m': 8.7, '1y': 15.67, 'ytd': 12.4, 'total': 15.67 },
    '3m': { '1d': 0.45, '1w': 2.1, '1m': 3.2, '3m': 8.7, '1y': 15.67, 'ytd': 12.4, 'total': 15.67 },
    '6m': { '1d': 0.45, '1w': 2.1, '1m': 3.2, '3m': 8.7, '1y': 15.67, 'ytd': 12.4, 'total': 15.67 },
    '1y': { '1d': 0.45, '1w': 2.1, '1m': 3.2, '3m': 8.7, '1y': 15.67, 'ytd': 12.4, 'total': 15.67 },
    'ytd': { '1d': 0.45, '1w': 2.1, '1m': 3.2, '3m': 8.7, '1y': 15.67, 'ytd': 12.4, 'total': 15.67 },
    'all': { '1d': 0.45, '1w': 2.1, '1m': 3.2, '3m': 8.7, '1y': 15.67, 'ytd': 12.4, 'total': 15.67 }
  }

  return baseReturns[period]?.[returnType] || baseReturns['1m'][returnType] || 0
}