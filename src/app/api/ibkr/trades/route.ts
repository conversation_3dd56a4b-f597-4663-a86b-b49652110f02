import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Mock data - replace with actual IBKR API calls
    const trades = Array.from({ length: 20 }, (_, i) => ({
      id: `trade_${i}`,
      symbol: ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA'][Math.floor(Math.random() * 5)],
      side: Math.random() > 0.5 ? 'BUY' : 'SELL' as 'BUY' | 'SELL',
      quantity: Math.floor(Math.random() * 100) + 1,
      price: Math.random() * 200 + 50,
      pnl: (Math.random() - 0.4) * 1000,
      date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString()
    }))

    return NextResponse.json(trades)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch trades' },
      { status: 500 }
    )
  }
}