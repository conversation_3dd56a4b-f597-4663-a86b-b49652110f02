import { NextResponse } from 'next/server'

// IBKR API compatible trade structure
interface IBKRTrade {
  id: string
  symbol: string
  side: 'BUY' | 'SELL'
  quantity: number
  returnPct: number // Percentage return instead of absolute P&L
  date: string
  // Hidden fields (not returned to client for privacy)
  // price: number
  // pnl: number
  // commission: number
}

export async function GET() {
  try {
    // Mock data matching IBKR API structure - replace with actual IBKR API calls
    // This structure hides monetary amounts and shows only percentage returns
    const symbols = ['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'NVDA', 'AMZN', 'META', 'NFLX', 'AMD', 'INTC']

    const trades: IBKRTrade[] = Array.from({ length: 25 }, (_, i) => {
      const isProfit = Math.random() > 0.35 // 65% win rate
      const returnPct = isProfit
        ? Math.random() * 8 + 0.5 // Positive returns: 0.5% to 8.5%
        : -(Math.random() * 5 + 0.2) // Negative returns: -0.2% to -5.2%

      return {
        id: `trade_${Date.now()}_${i}`,
        symbol: symbols[Math.floor(Math.random() * symbols.length)],
        side: Math.random() > 0.5 ? 'BUY' : 'SELL',
        quantity: Math.floor(Math.random() * 500) + 10, // 10 to 510 shares
        returnPct: Number(returnPct.toFixed(2)),
        date: new Date(Date.now() - i * Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString()
      }
    })

    // Sort by date (most recent first)
    trades.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

    return NextResponse.json(trades)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch trades' },
      { status: 500 }
    )
  }
}