import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Mock data - replace with actual IBKR API calls
    const chartData = Array.from({ length: 30 }, (_, i) => ({
      date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      value: Math.random() * 4 - 2, // Random daily return between -2% and 2%
      cumulative: Math.random() * 20 + 5 // Random cumulative return
    }))

    return NextResponse.json(chartData)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch chart data' },
      { status: 500 }
    )
  }
}