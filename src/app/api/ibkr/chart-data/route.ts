import { NextResponse } from 'next/server'

// IBKR API compatible chart data structure
interface IBKRChartDataPoint {
  t: number // timestamp in milliseconds (IBKR format)
  o: number // open price (percentage for portfolio)
  h: number // high price (percentage for portfolio)
  l: number // low price (percentage for portfolio)
  c: number // close price (percentage for portfolio)
  v: number // volume (not applicable for portfolio, set to 0)
}

interface IBKRChartResponse {
  symbol: string
  text: string
  data: IBKRChartDataPoint[]
  period: string
  barSize: string
}

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || '1m'
    const barSize = searchParams.get('barSize') || '1d'

    // Generate mock data based on period - replace with actual IBKR API calls
    const dataPoints = generateChartData(period, barSize)

    // IBKR API compatible response structure
    const chartResponse: IBKRChartResponse = {
      symbol: 'PORTFOL<PERSON>', // Portfolio performance
      text: 'Portfolio Performance',
      data: dataPoints,
      period: period,
      barSize: barSize
    }

    return NextResponse.json(chartResponse)
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to fetch chart data' },
      { status: 500 }
    )
  }
}

function generateChartData(period: string, barSize: string): IBKRChartDataPoint[] {
  const now = Date.now()
  let dataPoints: IBKRChartDataPoint[] = []
  let numPoints: number
  let intervalMs: number

  // Determine number of data points and interval based on period
  switch (period) {
    case '1d':
      numPoints = 24 // 24 hours
      intervalMs = 60 * 60 * 1000 // 1 hour
      break
    case '1w':
      numPoints = 7 // 7 days
      intervalMs = 24 * 60 * 60 * 1000 // 1 day
      break
    case '1m':
      numPoints = 30 // 30 days
      intervalMs = 24 * 60 * 60 * 1000 // 1 day
      break
    case '3m':
      numPoints = 90 // 90 days
      intervalMs = 24 * 60 * 60 * 1000 // 1 day
      break
    case '6m':
      numPoints = 180 // 180 days
      intervalMs = 24 * 60 * 60 * 1000 // 1 day
      break
    case '1y':
      numPoints = 365 // 365 days
      intervalMs = 24 * 60 * 60 * 1000 // 1 day
      break
    case 'ytd':
      const startOfYear = new Date(new Date().getFullYear(), 0, 1).getTime()
      numPoints = Math.floor((now - startOfYear) / (24 * 60 * 60 * 1000))
      intervalMs = 24 * 60 * 60 * 1000 // 1 day
      break
    case 'all':
      numPoints = 1000 // 1000 days (about 3 years)
      intervalMs = 24 * 60 * 60 * 1000 // 1 day
      break
    default:
      numPoints = 30
      intervalMs = 24 * 60 * 60 * 1000
  }

  // Generate cumulative performance data (percentage-based)
  let cumulativeReturn = 0
  const baseReturn = 15.67 // Total return percentage

  for (let i = 0; i < numPoints; i++) {
    const timestamp = now - (numPoints - 1 - i) * intervalMs

    // Generate realistic daily returns that build up to the total return
    const dailyReturn = (Math.random() - 0.5) * 2 + (baseReturn / numPoints) // Random walk with upward trend
    cumulativeReturn += dailyReturn

    // Ensure we don't go too negative
    if (cumulativeReturn < -20) {
      cumulativeReturn = -20 + Math.random() * 5
    }

    // Create OHLC data (for portfolio, these represent percentage returns)
    const open = i === 0 ? 0 : dataPoints[i - 1].c
    const close = cumulativeReturn
    const high = Math.max(open, close) + Math.random() * 0.5
    const low = Math.min(open, close) - Math.random() * 0.5

    dataPoints.push({
      t: timestamp,
      o: Number(open.toFixed(2)),
      h: Number(high.toFixed(2)),
      l: Number(low.toFixed(2)),
      c: Number(close.toFixed(2)),
      v: 0 // Volume not applicable for portfolio performance
    })
  }

  return dataPoints
}