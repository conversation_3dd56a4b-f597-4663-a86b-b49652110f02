'use client'

import { useState, useEffect } from 'react'
import { TradingMetrics } from '../components/TradingMetrics'
import { PerformanceChart } from '../components/PerformanceChart'
import { TradeHistory } from '../components/TradeHistory'
import { PortfolioAllocation } from '../components/PortfolioAllocation'
import { SectorBreakdown } from '../components/SectorBreakdown'
import { TimePeriodFilter, TimePeriod, formatPeriodForAPI } from '../components/TimePeriodFilter'

interface PerformanceMetrics {
  totalReturnPct: number
  dayReturnPct: number
  weekReturnPct: number
  monthReturnPct: number
  quarterReturnPct: number
  yearReturnPct: number
  ytdReturnPct: number
  maxDrawdownPct: number
  sharpeRatio: number
  winRatePct: number
  profitFactor: number
  totalTrades: number
}

interface PerformanceData {
  totalCashValue: {
    amount: number
    currency: string
  }
  netLiquidationValue: {
    amount: number
    currency: string
  }
  unrealizedPnl: {
    amount: number
    currency: string
  }
  realizedPnl: {
    amount: number
    currency: string
  }
  performanceMetrics: PerformanceMetrics
  period: string
  lastUpdated: string
}

export default function Dashboard() {
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedPeriod, setSelectedPeriod] = useState<TimePeriod>('1M')

  useEffect(() => {
    fetchPerformanceData(selectedPeriod)
  }, [selectedPeriod])

  const fetchPerformanceData = async (period: TimePeriod) => {
    setLoading(true)
    setError(null)
    try {
      const periodParam = formatPeriodForAPI(period)
      const response = await fetch(`/api/ibkr/performance?period=${periodParam}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      setPerformanceData(data)
    } catch (error) {
      console.error('Failed to fetch performance data:', error)
      setError(error instanceof Error ? error.message : 'Failed to load performance data')
      setPerformanceData(null)
    } finally {
      setLoading(false)
    }
  }

  const handlePeriodChange = (period: TimePeriod) => {
    setSelectedPeriod(period)
  }

  const handleRetry = () => {
    fetchPerformanceData(selectedPeriod)
  }

  if (loading && !performanceData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-xl text-gray-600">Loading portfolio performance...</div>
          <div className="text-sm text-gray-500 mt-2">Connecting to Interactive Brokers...</div>
        </div>
      </div>
    )
  }

  if (error && !performanceData) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center max-w-md mx-auto p-6">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to Load Data</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <button
            onClick={handleRetry}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Portfolio Performance Dashboard</h1>
              <p className="mt-1 text-sm text-gray-500">
                Interactive Brokers portfolio performance - percentage returns only
              </p>
            </div>
            <div className="mt-4 sm:mt-0">
              <TimePeriodFilter
                selectedPeriod={selectedPeriod}
                onPeriodChange={handlePeriodChange}
                className="flex-wrap"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {performanceData ? (
          <div className="space-y-8">
            {/* Performance Metrics */}
            <TradingMetrics
              data={performanceData}
              selectedPeriod={selectedPeriod}
            />

            {/* Main Performance Chart */}
            <div className="grid grid-cols-1 gap-6">
              <PerformanceChart
                selectedPeriod={selectedPeriod}
                className="w-full"
              />
            </div>

            {/* Portfolio Analysis Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {/* Portfolio Allocation */}
              <div className="lg:col-span-1">
                <PortfolioAllocation className="h-full" />
              </div>

              {/* Sector Breakdown */}
              <div className="lg:col-span-1">
                <SectorBreakdown className="h-full" />
              </div>

              {/* Trade History */}
              <div className="lg:col-span-2 xl:col-span-1">
                <TradeHistory className="h-full" />
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="text-gray-500">
              <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
              <p className="text-gray-500 mb-4">Unable to load portfolio performance data. Please try again later.</p>
              <button
                onClick={handleRetry}
                className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Retry
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="text-center text-sm text-gray-500">
            <p>
              This dashboard displays portfolio performance data from Interactive Brokers.
              All monetary amounts are hidden for privacy - only percentage returns are shown.
            </p>
            <p className="mt-1">
              Data is updated in real-time during market hours. Past performance does not guarantee future results.
            </p>
          </div>
        </div>
      </footer>
    </div>
  )
}