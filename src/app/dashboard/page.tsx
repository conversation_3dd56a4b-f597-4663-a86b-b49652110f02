'use client'

import { useState, useEffect } from 'react'
import { TradingMetrics } from '../components/TradingMetrics'
import { PerformanceChart } from '../components/PerformanceChart'
import { TradeHistory } from '../components/TradeHistory'

interface PerformanceData {
  totalReturn: number
  winRate: number
  sharpeRatio: number
  maxDrawdown: number
  totalTrades: number
  profitFactor: number
}

export default function Dashboard() {
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPerformanceData()
  }, [])

  const fetchPerformanceData = async () => {
    try {
      const response = await fetch('/api/ibkr/performance')
      const data = await response.json()
      setPerformanceData(data)
    } catch (error) {
      console.error('Failed to fetch performance data:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-xl">Loading performance data...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Trading Performance Dashboard</h1>
        
        {performanceData && (
          <>
            <TradingMetrics data={performanceData} />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
              <PerformanceChart />
              <TradeHistory />
            </div>
          </>
        )}
      </div>
    </div>
  )
}