import type { Metadata } from 'next'
import { WebVitals } from './components/WebVitals'
import './globals.css'

export const metadata: Metadata = {
  title: 'Trading Performance Dashboard',
  description: 'Real-time trading performance metrics and analytics',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <WebVitals />
        {children}
      </body>
    </html>
  )
}
